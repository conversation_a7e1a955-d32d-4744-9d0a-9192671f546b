package ai.yourouter.common.context;

import lombok.Data;
import org.springframework.util.StringUtils;

import java.time.Instant;
import java.util.HashMap;
import java.util.Map;

@Data
public class ChatRequestStatistic {

    private Long preparationTime;

    private boolean searchRequest = false;

    private HashMap<String, Object> rawRequest;

    private StringBuilder streamResponse = new StringBuilder();

    private Map<String, String> requestHeaders = new HashMap<>();

    private Long startRequestTime = Instant.now().toEpochMilli();

    private Map<String, String> requestParams = new HashMap<>();


    public boolean onStream() {
        return (boolean) rawRequest.getOrDefault("stream", false);
    }

    public String vendorHeader() {
        return requestHeaders.get("vendor");
    }

    public void setVendorHeader(String vendorHeader) {
        if (StringUtils.hasLength(vendorHeader())) {
            requestHeaders.put("vendor", vendorHeader() + "," + vendorHeader);
        } else {
            requestHeaders.put("vendor", vendorHeader);
        }
    }

    /**
     * latest: 2025-04-01-preview
     * <a href="https://learn.microsoft.com/en-us/azure/ai-foundry/openai/reference">...</a>
     * 2025-04-01-preview
     *
     * @return resolve version
     */
    public String azureVersion() {
        return requestParams.getOrDefault("api-version", "2025-04-01-preview");
    }

    public Long consumerTime() {
        return Instant.now().toEpochMilli() - startRequestTime;
    }
}
